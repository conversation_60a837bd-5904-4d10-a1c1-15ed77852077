import 'dart:async';
import 'dart:convert';
import 'package:http/http.dart' as http;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import '../models/user.dart';
import '../config/api_config.dart';
import 'storage_service.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  static const _storage = FlutterSecureStorage();
  static const String _tokenKey = 'jwt_token';
  static const String _userKey = 'current_user';

  factory AuthService() {
    return _instance;
  }

  AuthService._internal();

  User? _currentUser;
  String? _token;
  final _authStateController = StreamController<User?>.broadcast();

  Stream<User?> get authStateChanges => _authStateController.stream;
  User? get currentUser => _currentUser;
  String? get token => _token;

  Future<User> login(String email, String password) async {
    try {
      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.loginEndpoint}'),
        headers: {'Content-Type': 'application/json'},
        body: jsonEncode({
          'email': email,
          'password': password,
        }),
      );

      print('URL d\'authentification: ${ApiConfig.baseUrl}${ApiConfig.loginEndpoint}');
      print('Status code: ${response.statusCode}');

      if (response.statusCode == 200) {
        // Vérifier si la réponse est du HTML au lieu de JSON
        if (response.body.trim().startsWith('<!')) {
          print('Erreur: Le serveur a retourné du HTML au lieu de JSON pour l\'authentification');
          print('Réponse reçue: ${response.body.substring(0, 200)}...');
          throw Exception('Erreur serveur: Page HTML retournée au lieu de JSON');
        }

        try {
          final data = jsonDecode(response.body);
          if (data['status'] == 'success') {
            _token = data['token'];
            await _storage.write(key: _tokenKey, value: _token);

            final userData = data['user'];
            final user = User.fromApiResponse(userData);

            _currentUser = user;
            await _storage.write(key: _userKey, value: jsonEncode(user.toJson()));
            _authStateController.add(user);

            return user;
          } else {
            throw Exception(data['message'] ?? 'Erreur de connexion');
          }
        } catch (formatException) {
          print('Erreur de format JSON lors de l\'authentification: $formatException');
          print('Réponse reçue: ${response.body.substring(0, 200)}...');
          throw Exception('Erreur de format de réponse du serveur');
        }
      } else {
        final errorData = jsonDecode(response.body);
        throw Exception(errorData['message'] ?? 'Erreur de connexion');
      }
    } catch (e) {
      print('Erreur lors de la connexion: $e');
      rethrow;
    }
  }

  Future<void> logout() async {
    try {
      if (_token != null) {
        await http.post(
          Uri.parse('${ApiConfig.baseUrl}/auth/logout'),
          headers: {
            'Content-Type': 'application/json',
            'Authorization': 'Bearer $_token',
          },
        );
      }
    } catch (e) {
      print('Erreur lors de la déconnexion: $e');
    } finally {
      _currentUser = null;
      _token = null;
      await _storage.delete(key: _tokenKey);
      await _storage.delete(key: _userKey);
      _authStateController.add(null);
    }
  }

  Future<bool> refreshToken() async {
    try {
      if (_token == null) return false;

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}/auth/refresh'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success') {
          _token = data['token'];
          await _storage.write(key: _tokenKey, value: _token);
          return true;
        }
      }
      return false;
    } catch (e) {
      print('Erreur lors du rafraîchissement du token: $e');
      return false;
    }
  }

  Future<bool> verifyToken() async {
    try {
      if (_token == null) return false;

      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}/auth/verify'),
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer $_token',
        },
      );

      return response.statusCode == 200;
    } catch (e) {
      print('Erreur lors de la vérification du token: $e');
      return false;
    }
  }

  Future<void> loadStoredAuth() async {
    try {
      _token = await _storage.read(key: _tokenKey);
      final userJson = await _storage.read(key: _userKey);

      if (_token != null && userJson != null) {
        final isValid = await verifyToken();
        if (isValid) {
          _currentUser = User.fromJson(jsonDecode(userJson));
          _authStateController.add(_currentUser);
        } else {
          await logout();
        }
      }
    } catch (e) {
      print('Erreur lors du chargement de l\'authentification: $e');
      await logout();
    }
  }

  Map<String, String> getAuthHeaders() {
    return {
      'Content-Type': 'application/json',
      if (_token != null) 'Authorization': 'Bearer $_token',
    };
  }

  bool isAdmin() {
    return _currentUser?.role == 'admin' || _currentUser?.role == 'administrateur';
  }

  bool isHealthAgent() {
    return _currentUser?.role == 'docteur' || _currentUser?.role == 'health_agent';
  }

  void dispose() {
    _authStateController.close();
  }
}
