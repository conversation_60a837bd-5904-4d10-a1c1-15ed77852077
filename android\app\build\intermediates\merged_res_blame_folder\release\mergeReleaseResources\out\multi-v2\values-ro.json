{"logs": [{"outputFile": "com.example.ptccare_mobile.app-mergeReleaseResources-43:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2d2f5fa8bcbd0f3dad4b028732703ced\\transformed\\preference-1.2.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,176,262,337,476,645,732", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "171,257,332,471,640,727,808"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3578,3649,3735,3810,4133,4302,4389", "endColumns": "70,85,74,138,168,86,80", "endOffsets": "3644,3730,3805,3944,4297,4384,4465"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\3f7b9e1f0dd0be5c1900fb95a473ccc2\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2851,2949,3051,3151,3250,3352,3461,4032", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "2944,3046,3146,3245,3347,3456,3573,4128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8fda9e6cb848c30ef97e1cc21f649ac8\\transformed\\appcompat-1.1.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,918,1009,1101,1196,1290,1391,1484,1579,1673,1764,1856,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "221,325,438,522,626,747,832,913,1004,1096,1191,1285,1386,1479,1574,1668,1759,1851,1933,2045,2153,2253,2367,2473,2579,2743,2846,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,918,1009,1101,1196,1290,1391,1484,1579,1673,1764,1856,1938,2050,2158,2258,2372,2478,2584,2748,3949", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "221,325,438,522,626,747,832,913,1004,1096,1191,1285,1386,1479,1574,1668,1759,1851,1933,2045,2153,2253,2367,2473,2579,2743,2846,4027"}}]}]}