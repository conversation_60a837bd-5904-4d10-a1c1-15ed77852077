1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ptccare_mobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:3:5-66
11-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:3:22-64
12    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
12-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:4:5-80
12-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:4:22-78
13    <uses-permission android:name="android.permission.VIBRATE" />
13-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:5:5-66
13-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:5:22-63
14    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
14-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:6:5-81
14-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:6:22-78
15    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
15-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:7:5-79
15-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:7:22-76
16    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
16-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:8:5-76
16-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:8:22-74
17    <!--
18         Required to query activities that can process text, see:
19         https://developer.android.com/training/package-visibility and
20         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
21
22         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
23    -->
24    <queries>
24-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:45:5-50:15
25        <intent>
25-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:46:9-49:18
26            <action android:name="android.intent.action.PROCESS_TEXT" />
26-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:47:13-72
26-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:47:21-70
27
28            <data android:mimeType="text/plain" />
28-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:48:13-50
28-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:48:19-48
29        </intent>
30    </queries>
31
32    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
32-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
32-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
33
34    <permission
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
35        android:name="com.example.ptccare_mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
35-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
36        android:protectionLevel="signature" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
37
38    <uses-permission android:name="com.example.ptccare_mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
39
40    <application
41        android:name="android.app.Application"
41-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:11:9-42
42        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
43        android:extractNativeLibs="true"
44        android:icon="@mipmap/ic_launcher"
44-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:12:9-43
45        android:label="PTC Care" >
45-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:10:9-33
46        <activity
46-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:13:9-33:20
47            android:name="com.example.ptccare_mobile.MainActivity"
47-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:14:13-41
48            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
48-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:18:13-163
49            android:exported="true"
49-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:15:13-36
50            android:hardwareAccelerated="true"
50-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:19:13-47
51            android:launchMode="singleTop"
51-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:16:13-43
52            android:theme="@style/LaunchTheme"
52-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:17:13-47
53            android:windowSoftInputMode="adjustResize" >
53-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:20:13-55
54
55            <!--
56                 Specifies an Android theme to apply to this Activity as soon as
57                 the Android process has started. This theme is visible to the user
58                 while the Flutter UI initializes. After that, this theme continues
59                 to determine the Window background behind the Flutter UI.
60            -->
61            <meta-data
61-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:25:13-28:17
62                android:name="io.flutter.embedding.android.NormalTheme"
62-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:26:15-70
63                android:resource="@style/NormalTheme" />
63-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:27:15-52
64
65            <intent-filter>
65-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:29:13-32:29
66                <action android:name="android.intent.action.MAIN" />
66-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:30:17-68
66-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:30:25-66
67
68                <category android:name="android.intent.category.LAUNCHER" />
68-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:31:17-76
68-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:31:27-74
69            </intent-filter>
70        </activity>
71        <!--
72             Don't delete the meta-data below.
73             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
74        -->
75        <meta-data
75-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:36:9-38:33
76            android:name="flutterEmbedding"
76-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:37:13-44
77            android:value="2" />
77-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:38:13-30
78
79        <uses-library
79-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
80            android:name="androidx.window.extensions"
80-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
81            android:required="false" />
81-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
82        <uses-library
82-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
83            android:name="androidx.window.sidecar"
83-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
84            android:required="false" />
84-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
85
86        <provider
86-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
87            android:name="androidx.startup.InitializationProvider"
87-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
88            android:authorities="com.example.ptccare_mobile.androidx-startup"
88-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
89            android:exported="false" >
89-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
90            <meta-data
90-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
91                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
92                android:value="androidx.startup" />
92-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
93            <meta-data
93-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
94                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
94-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
95                android:value="androidx.startup" />
95-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
96        </provider>
97
98        <receiver
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
99            android:name="androidx.profileinstaller.ProfileInstallReceiver"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
100            android:directBootAware="false"
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
101            android:enabled="true"
101-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
102            android:exported="true"
102-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
103            android:permission="android.permission.DUMP" >
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
104            <intent-filter>
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
105                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
106            </intent-filter>
107            <intent-filter>
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
108                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
109            </intent-filter>
110            <intent-filter>
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
111                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
111-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
112            </intent-filter>
113            <intent-filter>
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
114                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
114-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
115            </intent-filter>
116        </receiver>
117    </application>
118
119</manifest>
