import 'package:flutter/material.dart';
import 'dart:async';
import '../../constants/colors.dart';
import '../../models/patient.dart';
import '../../services/patient_service.dart';
import '../../services/api_sync_service.dart';
import 'patient_form.dart';

class PatientListScreen extends StatefulWidget {
  final VoidCallback? onPatientsChanged;
  const PatientListScreen({Key? key, this.onPatientsChanged}) : super(key: key);

  @override
  State<PatientListScreen> createState() => _PatientListScreenState();
}

class _PatientListScreenState extends State<PatientListScreen> {
  final PatientService _patientService = PatientService();
  final ApiSyncService _syncService = ApiSyncService();
  List<Patient> _patients = [];
  bool _isLoading = true;
  int _currentPage = 1;
  static const int _patientsPerPage = 8;
  StreamSubscription<void>? _dataChangedSubscription;

  @override
  void initState() {
    super.initState();
    _loadPatients();
    _listenToDataChanges();
  }

  @override
  void dispose() {
    _dataChangedSubscription?.cancel();
    super.dispose();
  }

  void _listenToDataChanges() {
    _dataChangedSubscription = _syncService.dataChangedStream.listen((_) {
      // Recharger les patients quand les données changent
      _loadPatients();
    });
  }

  Future<void> _loadPatients() async {
    setState(() {
      _isLoading = true;
    });
    try {
      final patients = await _patientService.getAllPatients();
      patients.sort((a, b) => (b.id ?? 0).compareTo(a.id ?? 0));
      setState(() {
        _patients = patients;
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _navigateToAddPatient() async {
    final result = await Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PatientFormScreen(
          onPatientAdded: () async {
            await _loadPatients();
            if (widget.onPatientsChanged != null) widget.onPatientsChanged!();
          },
        ),
      ),
    );
    if (result == true) {
      await _loadPatients();
      if (widget.onPatientsChanged != null) widget.onPatientsChanged!();
    }
  }

  void _showActionsMenu(Patient patient) {
    showModalBottomSheet(
      context: context,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(10)),
      ),
      builder: (context) {
        return Container(
          padding: EdgeInsets.symmetric(vertical: 20),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              ListTile(
                leading: Icon(Icons.edit, color: Colors.orange),
                title: Text('Modifier'),
                onTap: () {
                  Navigator.pop(context); // Fermer le menu
                  _editPatient(patient);
                },
              ),
              ListTile(
                leading: Icon(Icons.visibility, color: AppColors.primaryBlue),
                title: Text('Voir les détails'),
                onTap: () {
                  Navigator.pop(context);
                  _showPatientDetailsDialog(patient);
                },
              ),
              ListTile(
                leading: Icon(Icons.delete, color: Colors.red),
                title: Text('Supprimer'),
                onTap: () {
                  Navigator.pop(context);
                  _deletePatient(patient);
                },
              ),
            ],
          ),
        );
      },
    );
  }

  void _editPatient(Patient patient) async {
    final result = await Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PatientFormScreen(
          patient: patient,
          onPatientAdded: () async {
            await _loadPatients();
            if (widget.onPatientsChanged != null) widget.onPatientsChanged!();
          },
        ),
      ),
    );
    if (result == true) {
      await _loadPatients();
      if (widget.onPatientsChanged != null) widget.onPatientsChanged!();
    }
  }

  void _deletePatient(Patient patient) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Confirmation'),
        content: Text('Voulez-vous vraiment supprimer ce patient ?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: Text('Annuler'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              try {
                await PatientService().deletePatient(patient.id!);
                await _loadPatients();
                if (widget.onPatientsChanged != null) widget.onPatientsChanged!();
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Patient supprimé avec succès')),
                );
              } catch (e) {
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Erreur lors de la suppression : $e'),
                    backgroundColor: Colors.red,
                  ),
                );
              }
            },
            child: Text('Supprimer', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _showPatientDetailsDialog(Patient patient) {
    showDialog(
      context: context,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(20),
        ),
        child: Container(
          width: double.maxFinite,
          constraints: BoxConstraints(maxHeight: MediaQuery.of(context).size.height * 0.8),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // En-tête
              Container(
                padding: EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue,
                  borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Flexible(
                      child: Text(
                        'Détails du patient',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    IconButton(
                      icon: Icon(Icons.close, color: Colors.white),
                      padding: EdgeInsets.zero,
                      constraints: BoxConstraints(),
                      onPressed: () => Navigator.pop(context),
                    ),
                  ],
                ),
              ),
              // Contenu
              Flexible(
                child: SingleChildScrollView(
                  padding: EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailSection(
                        'Informations personnelles',
                        [
                          _buildDetailRow('Nom', patient.lastname),
                          _buildDetailRow('Prénom', patient.firstname),
                          _buildDetailRow('Sexe', patient.sexe),
                          _buildDetailRow('Date de naissance', patient.birthDate),
                          _buildDetailRow('Lieu de naissance', patient.birthPlace),
                          _buildDetailRow('Adresse', patient.address),
                          _buildDetailRow('Téléphone', patient.tel),
                        ],
                      ),
                      SizedBox(height: 20),
                      _buildDetailSection(
                        'Informations médicales',
                        [
                          _buildDetailRow('Date 1ère CPN', patient.firstCpnDate),
                          _buildDetailRow('Terme grossesse', '${patient.pregnancyTerm}'),
                          _buildDetailRow('Date prévue accouchement', patient.expectedDeliveryDate),
                          _buildDetailRow('Date prochain rendez-vous', patient.nextAppointmentDate),
                          _buildDetailRow('Date fin grossesse', patient.pregnancyEndDate),
                        ],
                      ),
                      SizedBox(height: 20),
                      _buildDetailSection(
                        'Informations complémentaires',
                        [
                          _buildDetailRow('Langue', patient.language),
                          _buildDetailRow('Niveau d\'étude', patient.studyLevel),
                          _buildDetailRow('Profession', patient.occupation),
                          _buildDetailRow('Nom du mari', patient.husbandName),
                          _buildDetailRow('Téléphone du mari', patient.husbandTel),
                          _buildDetailRow('Assurance', patient.assurance),
                          _buildDetailRow('Consentement SMS', patient.smsConsent == 1 ? 'Oui' : 'Non'),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailSection(String title, List<Widget> children) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Container(
          padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
          decoration: BoxDecoration(
            color: AppColors.primaryBlue.withOpacity(0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Text(
            title,
            style: TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: AppColors.primaryBlue,
            ),
          ),
        ),
        SizedBox(height: 12),
        ...children,
      ],
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: 8, horizontal: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Expanded(
            flex: 2,
            child: Text(
              label,
              style: TextStyle(
                fontWeight: FontWeight.w500,
                color: Colors.grey[700],
              ),
            ),
          ),
          SizedBox(width: 16),
          Expanded(
            flex: 3,
            child: Text(
              value,
              style: TextStyle(
                color: Colors.black87,
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: const Text('Patients', style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white)),
        centerTitle: true,
        backgroundColor: AppColors.primaryBlue,
        elevation: 1,
        automaticallyImplyLeading: false,
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 50),
              _buildTableTitle(),
              const SizedBox(height: 16),
              _isLoading
                  ? Expanded(child: Center(child: CircularProgressIndicator()))
                  : _buildTable(),
              if (_patients.length > _patientsPerPage) _buildPagination(),
            ],
          ),
        ),
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: _navigateToAddPatient,
        backgroundColor: AppColors.primaryBlue,
        child: const Icon(Icons.person_add, color: Colors.white),
      ),
    );
  }

  Widget _buildTableTitle() {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.symmetric(vertical: 12),
      decoration: BoxDecoration(
        color: AppColors.primaryBlue,
        borderRadius: BorderRadius.circular(4),
      ),
      alignment: Alignment.center,
      child: const Text(
        'Liste des patients ajoutés',
        style: TextStyle(
          color: Colors.white,
          fontWeight: FontWeight.w500,
          fontSize: 16,
        ),
      ),
    );
  }

  Widget _buildTable() {
    if (_patients.isEmpty) {
      return Expanded(
        child: Center(
          child: Text(
            'Aucun patient ajouté',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        ),
      );
    }
    final int start = (_currentPage - 1) * _patientsPerPage;
    final int end = (_currentPage * _patientsPerPage).clamp(0, _patients.length);
    final List<Patient> patientsToShow = _patients.sublist(start, end);
    return Expanded(
      child: Column(
        children: [
          _buildTableHeader(),
          Expanded(
            child: _buildPatientsList(patientsToShow, start),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
      decoration: const BoxDecoration(
        border: Border(
          bottom: BorderSide(color: Colors.black12),
        ),
      ),
      child: Row(
        children: [
          _buildHeaderCell('N°', flex: 1),
          _buildHeaderCell('Nom & Prénom', flex: 3),
          _buildHeaderCell('Ajouté le', flex: 2),
          const Expanded(flex: 2, child: SizedBox()), // Pas d'en-tête pour Action
        ],
      ),
    );
  }

  Widget _buildHeaderCell(String text, {required int flex}) {
    return Expanded(
      flex: flex,
      child: Text(
        text,
        style: const TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildPatientsList(List<Patient> patients, int start) {
    return ListView.builder(
      padding: EdgeInsets.zero,
      itemCount: patients.length,
      itemBuilder: (context, index) {
        final patient = patients[index];
        final isEven = index.isEven;
        final orderNumber = start + index + 1;
        final name = '${patient.firstname} ${patient.lastname}';
        return Container(
          padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 4),
          decoration: BoxDecoration(
            color: isEven ? Color(0xFFF5F5F5) : Colors.white,
            border: const Border(
              bottom: BorderSide(color: Colors.black12),
            ),
          ),
          child: Row(
            children: [
              Expanded(
                flex: 1,
                child: Text(
                  orderNumber.toString().padLeft(2, '0'),
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 3,
                child: Text(
                  name,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 2,
                child: Text(
                  patient.createdAt,
                  style: const TextStyle(fontSize: 14),
                  overflow: TextOverflow.ellipsis,
                ),
              ),
              Expanded(
                flex: 2,
                child: Align(
                  alignment: Alignment.center,
                  child: InkWell(
                    onTap: () => _showActionsMenu(patient),
                    child: Icon(
                      Icons.more_vert,
                      size: 18,
                      color: AppColors.primaryBlue,
                    ),
                  ),
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildPagination() {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: List.generate((_patients.length / _patientsPerPage).ceil(), (index) {
          final page = index + 1;
          return InkWell(
            onTap: () {
              setState(() {
                _currentPage = page;
              });
            },
            child: Container(
              margin: const EdgeInsets.symmetric(horizontal: 4),
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: page == _currentPage ? AppColors.primaryBlue.withOpacity(0.1) : Colors.transparent,
                borderRadius: BorderRadius.circular(4),
              ),
              child: Text(
                '$page',
                style: TextStyle(
                  color: page == _currentPage ? AppColors.primaryBlue : Colors.grey,
                  fontWeight: page == _currentPage ? FontWeight.bold : FontWeight.normal,
                ),
              ),
            ),
          );
        }),
      ),
    );
  }
}
