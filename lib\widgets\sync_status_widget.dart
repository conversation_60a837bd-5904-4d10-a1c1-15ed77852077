import 'package:flutter/material.dart';
import 'dart:async';
import '../services/api_sync_service.dart';
import '../models/sync_models.dart';
import '../constants/colors.dart';

class SyncStatusWidget extends StatefulWidget {
  final bool showManualSyncButton;
  final VoidCallback? onSyncComplete;

  const SyncStatusWidget({
    Key? key,
    this.showManualSyncButton = true,
    this.onSyncComplete,
  }) : super(key: key);

  @override
  State<SyncStatusWidget> createState() => _SyncStatusWidgetState();
}

class _SyncStatusWidgetState extends State<SyncStatusWidget> {
  final _syncService = ApiSyncService();
  StreamSubscription<SyncStatus>? _statusSubscription;
  StreamSubscription<SyncProgress>? _progressSubscription;
  
  SyncStatus _currentStatus = SyncStatus.idle();
  SyncProgress? _currentProgress;

  @override
  void initState() {
    super.initState();
    _listenToSyncUpdates();
  }

  @override
  void dispose() {
    _statusSubscription?.cancel();
    _progressSubscription?.cancel();
    super.dispose();
  }

  void _listenToSyncUpdates() {
    _statusSubscription = _syncService.syncStatusStream.listen((status) {
      if (mounted) {
        setState(() {
          _currentStatus = status;
        });
        
        // Notifier la fin de synchronisation
        if (status.type == SyncStatusType.success && widget.onSyncComplete != null) {
          widget.onSyncComplete!();
        }
      }
    });

    _progressSubscription = _syncService.syncProgressStream.listen((progress) {
      if (mounted) {
        setState(() {
          _currentProgress = progress;
        });
      }
    });
  }

  Future<void> _performManualSync() async {
    try {
      await _syncService.performFullSync();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Erreur de synchronisation: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: EdgeInsets.all(16),
      child: Padding(
        padding: EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // En-tête avec icône de statut
            Row(
              children: [
                _buildStatusIcon(),
                SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Synchronisation',
                        style: TextStyle(
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      Text(
                        _currentStatus.message,
                        style: TextStyle(
                          fontSize: 14,
                          color: Colors.grey.shade600,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.showManualSyncButton)
                  _buildSyncButton(),
              ],
            ),
            
            // Barre de progression si synchronisation en cours
            if (_currentStatus.type == SyncStatusType.syncing && _currentProgress != null) ...[
              SizedBox(height: 16),
              _buildProgressIndicator(),
            ],
            
            // Informations de dernière synchronisation
            if (_syncService.lastSyncTime != null) ...[
              SizedBox(height: 12),
              Text(
                'Dernière synchronisation: ${_formatDateTime(_syncService.lastSyncTime!)}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey.shade500,
                ),
              ),
            ],
            
            // Détails d'erreur si applicable
            if (_currentStatus.type == SyncStatusType.error && _currentStatus.errorDetails != null) ...[
              SizedBox(height: 12),
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.shade50,
                  borderRadius: BorderRadius.circular(4),
                  border: Border.all(color: Colors.red.shade200),
                ),
                child: Text(
                  _currentStatus.errorDetails!,
                  style: TextStyle(
                    fontSize: 12,
                    color: Colors.red.shade700,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatusIcon() {
    switch (_currentStatus.type) {
      case SyncStatusType.idle:
        return Icon(Icons.sync, color: Colors.grey);
      case SyncStatusType.syncing:
        return SizedBox(
          width: 24,
          height: 24,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
          ),
        );
      case SyncStatusType.success:
        return Icon(Icons.check_circle, color: Colors.green);
      case SyncStatusType.error:
        return Icon(Icons.error, color: Colors.red);
      case SyncStatusType.offline:
        return Icon(Icons.wifi_off, color: Colors.orange);
    }
  }

  Widget _buildSyncButton() {
    final isLoading = _currentStatus.type == SyncStatusType.syncing;
    
    return ElevatedButton.icon(
      onPressed: isLoading ? null : _performManualSync,
      icon: isLoading 
        ? SizedBox(
            width: 16,
            height: 16,
            child: CircularProgressIndicator(
              strokeWidth: 2,
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          )
        : Icon(Icons.sync, size: 16),
      label: Text(isLoading ? 'Sync...' : 'Synchroniser'),
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.primaryBlue,
        foregroundColor: Colors.white,
        padding: EdgeInsets.symmetric(horizontal: 12, vertical: 8),
      ),
    );
  }

  Widget _buildProgressIndicator() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              _currentProgress!.operation,
              style: TextStyle(fontSize: 12),
            ),
            Text(
              '${_currentProgress!.current}/${_currentProgress!.total}',
              style: TextStyle(fontSize: 12, fontWeight: FontWeight.bold),
            ),
          ],
        ),
        SizedBox(height: 8),
        LinearProgressIndicator(
          value: _currentProgress!.percentage / 100,
          backgroundColor: Colors.grey.shade300,
          valueColor: AlwaysStoppedAnimation<Color>(AppColors.primaryBlue),
        ),
      ],
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day.toString().padLeft(2, '0')}/${dateTime.month.toString().padLeft(2, '0')}/${dateTime.year} à ${dateTime.hour.toString().padLeft(2, '0')}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
