import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import 'package:flutter/foundation.dart';
import '../models/patient.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  Database? _database;
  bool _isClosing = false;
  static bool _initialized = false;

  factory DatabaseService() {
    return _instance;
  }

  DatabaseService._internal() {
    _initializeDatabaseFactory();
  }

  static void _initializeDatabaseFactory() {
    if (!_initialized) {
      if (kIsWeb) {
        // Pour le web, on utilise une approche différente
        print('Mode web détecté - Base de données en mémoire');
      }
      _initialized = true;
    }
  }

  Future<Database> get database async {
    if (_database != null && (_database!.isOpen)) {
      return _database!;
    }
    _database = await _initDatabase();
    return _database!;
  }

  Future<bool> isDatabaseOpen() async {
    return _database != null && _database!.isOpen;
  }

  Future<void> ensureOpen() async {
    if (!await isDatabaseOpen()) {
      _database = await _initDatabase();
    }
    // Vérifier et créer la table agent si nécessaire
    await _ensureTableExists();
  }

  Future<void> _ensureTableExists() async {
    if (_database == null || !_database!.isOpen) return;
    
    try {
      // Vérifier si la table agent existe
      final tablesAgent = await _database!.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='agent'"
      );
      
      if (tablesAgent.isEmpty) {
        print('La table agent n\'existe pas, création...');
        await _database!.execute('''
          CREATE TABLE IF NOT EXISTS agent (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id INTEGER,
            firstname TEXT,
            lastname TEXT,
            tel TEXT,
            email TEXT,
            hospital_id INTEGER,
            created_by_id INTEGER,
            created_at TEXT,
            updated_at TEXT,
            synced INTEGER DEFAULT 0,
            FOREIGN KEY (hospital_id) REFERENCES hospital(id),
            FOREIGN KEY (created_by_id) REFERENCES user(id)
          )
        ''');
        print('Table agent créée avec succès');
      } else {
        print('La table agent existe déjà');
        
        // Vérifier que toutes les colonnes existent
        final columns = await _database!.rawQuery('PRAGMA table_info(agent)');
        print('Colonnes de la table agent: ${columns.map((c) => c['name']).toList()}');
      }
      
      // Vérifier si la table hospital existe
      final tablesHospital = await _database!.rawQuery(
        "SELECT name FROM sqlite_master WHERE type='table' AND name='hospital'"
      );
      
      if (tablesHospital.isEmpty) {
        print('La table hospital n\'existe pas, création...');
        await _database!.execute('''
          CREATE TABLE IF NOT EXISTS hospital (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            server_id INTEGER,
            name TEXT,
            address TEXT,
            tel TEXT
          )
        ''');
        print('Table hospital créée avec succès');
      } else {
        print('La table hospital existe déjà');
      }
    } catch (e) {
      print('Erreur lors de la vérification ou création des tables: $e');
    }
  }

  Future<Database> _initDatabase() async {
    try {
      if (kIsWeb) {
        // Pour le web, utiliser une base en mémoire
        return await openDatabase(
          inMemoryDatabasePath,
          version: 1,
          onCreate: (Database db, int version) async {
            await _createTables(db, version);
          },
        );
      } else {
        // Pour mobile, utiliser le chemin normal
        String path = join(await getDatabasesPath(), 'ptccare.db');
        print('Initialisation de la base de données: $path');
        return await openDatabase(
          path,
          version: 1,
          onCreate: (Database db, int version) async {
            await _createTables(db, version);
          },
        );
      }
    } catch (e) {
      print('Erreur lors de l\'initialisation de la base de données: $e');
      rethrow;
    }
  }

  Future<void> _createTables(Database db, int version) async {
    print('Création des tables de la base de données');
    // Suppression des anciennes tables non utilisées
    await db.execute('DROP TABLE IF EXISTS patients');
    await db.execute('DROP TABLE IF EXISTS health_agents');
    await db.execute('DROP TABLE IF EXISTS hospitals');
    // Création des tables à jour
    await db.execute('''
            CREATE TABLE IF NOT EXISTS user (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              server_id INTEGER,
              username TEXT,
              email TEXT,
              role TEXT,
              token TEXT
            )
    ''');
    await db.execute('''
            CREATE TABLE IF NOT EXISTS hospital (
              id INTEGER PRIMARY KEY AUTOINCREMENT,
              server_id INTEGER,
              name TEXT,
              address TEXT,
              tel TEXT
            )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS agent (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id INTEGER,
        firstname TEXT,
        lastname TEXT,
        tel TEXT,
        email TEXT,
        hospital_id INTEGER,
        created_by_id INTEGER,
        created_at TEXT,
        updated_at TEXT,
        synced INTEGER DEFAULT 0,
        FOREIGN KEY (hospital_id) REFERENCES hospital(id),
        FOREIGN KEY (created_by_id) REFERENCES user(id)
      )
    ''');
    await db.execute('''
      CREATE TABLE IF NOT EXISTS patient (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        server_id INTEGER,
        firstname TEXT,
        lastname TEXT,
        sexe TEXT,
        birth_date TEXT,
        birth_place TEXT,
        tel TEXT,
        address TEXT,
        husband_name TEXT,
        husband_tel TEXT,
        assurance TEXT,
        occupation TEXT,
        study_level TEXT,
        language TEXT,
        first_cpn_date TEXT,
        pregnancy_term INTEGER,
        expected_delivery_date TEXT,
        next_appointment_date TEXT,
        pregnancy_end_date TEXT,
        sms_consent INTEGER,
        agent_id INTEGER,
        agent_server_id INTEGER,
        created_at TEXT,
        updated_at TEXT,
        sync_status INTEGER DEFAULT 0,
        last_sync_date TEXT,
        FOREIGN KEY (agent_id) REFERENCES agent(id)
      )
    ''');

    // Ajouter la table notifications
    await db.execute('''
      CREATE TABLE IF NOT EXISTS notifications (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        title TEXT,
        message TEXT,
        date TEXT,
        type TEXT,
        read INTEGER DEFAULT 0
      )
    ''');
  }

  // Ne fermez pas la base de données sauf si vraiment nécessaire
  Future<void> closeDatabase() async {
    if (_database != null && _database!.isOpen && !_isClosing) {
      _isClosing = true;
      try {
        await _database!.close();
        _database = null;
        print('Base de données fermée avec succès');
      } catch (e) {
        print('Erreur lors de la fermeture de la base de données: $e');
      } finally {
        _isClosing = false;
      }
    }
  }

  // Ajouter cette méthode avant ensureOpen
  Future<void> recreateAllTables() async {
    try {
      print('Tentative de recréation de toutes les tables...');
      
      // S'assurer que la BD est ouverte
      if (_database == null || !_database!.isOpen) {
        _database = await _initDatabase();
      }
      
      final db = _database!;
      
      // Créer les tables principales
      await db.execute('''
        CREATE TABLE IF NOT EXISTS user (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          server_id INTEGER,
          username TEXT,
          email TEXT,
          role TEXT,
          token TEXT
        )
      ''');
      
      await db.execute('''
        CREATE TABLE IF NOT EXISTS hospital (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          server_id INTEGER,
          name TEXT,
          address TEXT,
          tel TEXT
        )
      ''');
      
      await db.execute('''
        CREATE TABLE IF NOT EXISTS agent (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          server_id INTEGER,
          firstname TEXT,
          lastname TEXT,
          tel TEXT,
          email TEXT,
          hospital_id INTEGER,
          created_by_id INTEGER,
          created_at TEXT,
          updated_at TEXT,
          synced INTEGER DEFAULT 0,
          FOREIGN KEY (hospital_id) REFERENCES hospital(id),
          FOREIGN KEY (created_by_id) REFERENCES user(id)
        )
      ''');
      
      await db.execute('''
        CREATE TABLE IF NOT EXISTS patient (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          server_id INTEGER,
          firstname TEXT,
          lastname TEXT,
          sexe TEXT,
          birth_date TEXT,
          birth_place TEXT,
          tel TEXT,
          address TEXT,
          husband_name TEXT,
          husband_tel TEXT,
          assurance TEXT,
          occupation TEXT,
          study_level TEXT,
          language TEXT,
          first_cpn_date TEXT,
          pregnancy_term INTEGER,
          expected_delivery_date TEXT,
          next_appointment_date TEXT,
          pregnancy_end_date TEXT,
          sms_consent INTEGER,
          agent_id INTEGER,
          agent_server_id INTEGER,
          created_at TEXT,
          updated_at TEXT,
          sync_status INTEGER DEFAULT 0,
          last_sync_date TEXT,
          FOREIGN KEY (agent_id) REFERENCES agent(id)
        )
      ''');
      
      await db.execute('''
        CREATE TABLE IF NOT EXISTS notifications (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          title TEXT,
          message TEXT,
          date TEXT,
          type TEXT,
          read INTEGER DEFAULT 0
        )
      ''');
      
      print('Toutes les tables ont été recréées avec succès');
    } catch (e) {
      print('Erreur lors de la recréation des tables: $e');
      rethrow;
    }
  }

  /// Récupère les patients non synchronisés (sync_status = 0)
  Future<List<Patient>> getUnsyncedPatients() async {
    try {
      final db = await database;
      final List<Map<String, dynamic>> maps = await db.query(
        'patient',
        where: 'sync_status = ?',
        whereArgs: [0],
      );

      return List.generate(maps.length, (i) {
        return Patient.fromMap(maps[i]);
      });
    } catch (e) {
      print('Erreur lors de la récupération des patients non synchronisés: $e');
      return [];
    }
  }

  /// Met à jour le server_id d'un patient après synchronisation
  Future<void> updatePatientServerId(int patientId, int serverId) async {
    try {
      final db = await database;
      await db.update(
        'patient',
        {
          'server_id': serverId,
          'sync_status': 1,
          'updated_at': DateTime.now().toIso8601String(),
        },
        where: 'id = ?',
        whereArgs: [patientId],
      );
      print('Patient $patientId mis à jour avec server_id: $serverId');
    } catch (e) {
      print('Erreur lors de la mise à jour du server_id: $e');
      rethrow;
    }
  }

  /// Insère ou met à jour un patient reçu du serveur
  Future<void> insertOrUpdatePatientFromServer(Patient patient) async {
    try {
      final db = await database;

      // Vérifier si le patient existe déjà (par server_id)
      if (patient.serverId != null) {
        final existing = await db.query(
          'patient',
          where: 'server_id = ?',
          whereArgs: [patient.serverId],
        );

        if (existing.isNotEmpty) {
          // Mettre à jour le patient existant
          await db.update(
            'patient',
            patient.toMap(),
            where: 'server_id = ?',
            whereArgs: [patient.serverId],
          );
          print('Patient mis à jour depuis le serveur: ${patient.firstname} ${patient.lastname}');
        } else {
          // Insérer un nouveau patient
          await db.insert(
            'patient',
            patient.toMap(),
            conflictAlgorithm: ConflictAlgorithm.replace,
          );
          print('Nouveau patient inséré depuis le serveur: ${patient.firstname} ${patient.lastname}');
        }
      }
    } catch (e) {
      print('Erreur lors de l\'insertion/mise à jour du patient du serveur: $e');
      rethrow;
    }
  }

  /// Marque tous les patients comme non synchronisés (utile pour forcer une resynchronisation)
  Future<void> markAllPatientsAsUnsynced() async {
    try {
      final db = await database;
      await db.update(
        'patient',
        {'sync_status': 0},
      );
      print('Tous les patients marqués comme non synchronisés');
    } catch (e) {
      print('Erreur lors du marquage des patients: $e');
      rethrow;
    }
  }

  /// Compte le nombre de patients non synchronisés
  Future<int> getUnsyncedPatientsCount() async {
    try {
      final db = await database;
      final result = await db.rawQuery(
        'SELECT COUNT(*) as count FROM patient WHERE sync_status = 0'
      );
      return Sqflite.firstIntValue(result) ?? 0;
    } catch (e) {
      print('Erreur lors du comptage des patients non synchronisés: $e');
      return 0;
    }
  }
}
