# Script d'installation automatique pour contourner le bug Flutter
Write-Host "🔨 Compilation de l'APK..." -ForegroundColor Yellow
flutter build apk --debug

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ Compilation réussie!" -ForegroundColor Green
    
    Write-Host "📱 Installation sur l'appareil..." -ForegroundColor Yellow
    C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe install -r android\app\build\outputs\apk\debug\app-debug.apk
    
    if ($LASTEXITCODE -eq 0) {
        Write-Host "🎉 Installation réussie!" -ForegroundColor Green
        Write-Host "🚀 Lancement de l'application..." -ForegroundColor Yellow
        C:\Users\<USER>\AppData\Local\Android\sdk\platform-tools\adb.exe shell am start -n com.example.ptccare_mobile/.MainActivity
    } else {
        Write-Host "❌ Erreur lors de l'installation" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Erreur lors de la compilation" -ForegroundColor Red
}