1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.example.ptccare_mobile"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="21"
9        android:targetSdkVersion="34" />
10    <!--
11         The INTERNET permission is required for development. Specifically,
12         the Flutter tool needs it to communicate with the running application
13         to allow setting breakpoints, to provide hot reload, etc.
14    -->
15    <uses-permission android:name="android.permission.INTERNET" />
15-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:3:5-66
15-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:3:22-64
16    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
16-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:4:5-80
16-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:4:22-78
17    <uses-permission android:name="android.permission.VIBRATE" />
17-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:5:5-66
17-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:5:22-63
18    <uses-permission android:name="android.permission.USE_FULL_SCREEN_INTENT" />
18-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:6:5-81
18-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:6:22-78
19    <uses-permission android:name="android.permission.SCHEDULE_EXACT_ALARM" />
19-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:7:5-79
19-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:7:22-76
20    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
20-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:8:5-76
20-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:8:22-74
21    <!--
22 Required to query activities that can process text, see:
23         https://developer.android.com/training/package-visibility and
24         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
25
26         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
27    -->
28    <queries>
28-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:45:5-50:15
29        <intent>
29-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:46:9-49:18
30            <action android:name="android.intent.action.PROCESS_TEXT" />
30-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:47:13-72
30-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:47:21-70
31
32            <data android:mimeType="text/plain" />
32-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:48:13-50
32-->E:\ptccare_mobile\android\app\src\main\AndroidManifest.xml:48:19-48
33        </intent>
34    </queries>
35
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:5-79
36-->[:connectivity_plus] C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\connectivity_plus-6.1.4\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:7:22-76
37
38    <permission
38-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
39        android:name="com.example.ptccare_mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
39-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
40        android:protectionLevel="signature" />
40-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
41
42    <uses-permission android:name="com.example.ptccare_mobile.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
42-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
43
44    <application
45        android:name="android.app.Application"
46        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
46-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\transforms-3\3f7b9e1f0dd0be5c1900fb95a473ccc2\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
47        android:debuggable="true"
48        android:extractNativeLibs="true"
49        android:icon="@mipmap/ic_launcher"
50        android:label="PTC Care" >
51        <activity
52            android:name="com.example.ptccare_mobile.MainActivity"
53            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
54            android:exported="true"
55            android:hardwareAccelerated="true"
56            android:launchMode="singleTop"
57            android:theme="@style/LaunchTheme"
58            android:windowSoftInputMode="adjustResize" >
59
60            <!--
61                 Specifies an Android theme to apply to this Activity as soon as
62                 the Android process has started. This theme is visible to the user
63                 while the Flutter UI initializes. After that, this theme continues
64                 to determine the Window background behind the Flutter UI.
65            -->
66            <meta-data
67                android:name="io.flutter.embedding.android.NormalTheme"
68                android:resource="@style/NormalTheme" />
69
70            <intent-filter>
71                <action android:name="android.intent.action.MAIN" />
72
73                <category android:name="android.intent.category.LAUNCHER" />
74            </intent-filter>
75        </activity>
76        <!--
77             Don't delete the meta-data below.
78             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
79        -->
80        <meta-data
81            android:name="flutterEmbedding"
82            android:value="2" />
83
84        <uses-library
84-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
85            android:name="androidx.window.extensions"
85-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
86            android:required="false" />
86-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
87        <uses-library
87-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
88            android:name="androidx.window.sidecar"
88-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
89            android:required="false" />
89-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\404e077340ae5c2cfe7e02c3a2f40788\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
90
91        <provider
91-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
92            android:name="androidx.startup.InitializationProvider"
92-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
93            android:authorities="com.example.ptccare_mobile.androidx-startup"
93-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
94            android:exported="false" >
94-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
95            <meta-data
95-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
96                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
96-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
97                android:value="androidx.startup" />
97-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\4f754d0c56cc243af370ee2222114cb6\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
98            <meta-data
98-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
99                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
99-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
100                android:value="androidx.startup" />
100-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
101        </provider>
102
103        <receiver
103-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
104            android:name="androidx.profileinstaller.ProfileInstallReceiver"
104-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
105            android:directBootAware="false"
105-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
106            android:enabled="true"
106-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
107            android:exported="true"
107-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
108            android:permission="android.permission.DUMP" >
108-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
109            <intent-filter>
109-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
110                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
110-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
111            </intent-filter>
112            <intent-filter>
112-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
113                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
113-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
114            </intent-filter>
115            <intent-filter>
115-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
116                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
116-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
117            </intent-filter>
118            <intent-filter>
118-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
119                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
119-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\59189819f63dc2a42396c511328b0658\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
120            </intent-filter>
121        </receiver>
122    </application>
123
124</manifest>
