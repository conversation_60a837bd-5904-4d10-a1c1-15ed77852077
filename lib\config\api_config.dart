class ApiConfig {
  static const String baseUrl = 'https://ptccare-web-ae382d4ad8cc.herokuapp.com/api/mobile/';

  // Endpoints d'authentification (sans slash initial car baseUrl finit par /)
  static const String loginEndpoint = 'auth/login';
  static const String logoutEndpoint = 'auth/logout';
  static const String refreshEndpoint = 'auth/refresh';
  static const String verifyEndpoint = 'auth/verify';

  // Endpoints de données (sans slash initial car baseUrl finit par /)
  static const String initialDataEndpoint = 'initial-data';
  static const String userDataEndpoint = 'user-data';

  // Configuration des timeouts
  static const Duration connectionTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);

  // Configuration JWT
  static const int tokenExpirationDays = 7;
  static const String tokenType = 'Bearer';
}