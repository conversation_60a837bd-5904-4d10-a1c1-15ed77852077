org.gradle.jvmargs=-Xmx4096m -XX:MaxMetaspaceSize=512m -XX:+HeapDumpOnOutOfMemoryError -Dfile.encoding=UTF-8
org.gradle.java.home=C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot
android.useAndroidX=true
android.enableJetifier=true

# Optimisations Gradle
org.gradle.parallel=true
org.gradle.caching=true
org.gradle.configureondemand=true
org.gradle.daemon=true
# Décommentez et ajustez le chemin après installation de Java 11
# org.gradle.java.home=C:/Program Files/Eclipse Adoptium/jdk-*********-hotspot
org.gradle.java.version=17
