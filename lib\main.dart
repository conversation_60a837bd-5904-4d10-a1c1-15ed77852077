// @dart=2.9
import 'package:flutter/material.dart';
import 'package:flutter/rendering.dart';
import 'package:ptccare_mobile/screens/splash/logo_screen.dart';
import 'package:ptccare_mobile/services/database_service.dart';
import 'package:ptccare_mobile/services/auth_service.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  try {
    print('Initialisation de la base de données...');
    final databaseService = DatabaseService();
    await databaseService.ensureOpen();
    print('Base de données initialisée avec succès');

    print('Initialisation de l\'authentification...');
    final authService = AuthService();
    await authService.loadStoredAuth();
    print('Authentification initialisée avec succès');
  } catch (e) {
    print('Erreur lors de l\'initialisation: $e');
  }

  debugPaintSizeEnabled = false;
  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  // This widget is the root of your application.
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      debugShowCheckedModeBanner: false,
      title: 'PTCCARE Mobile',
      theme: ThemeData(
        colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
        useMaterial3: true,
      ),
      home: LogoScreen(),
    );
  }
}
