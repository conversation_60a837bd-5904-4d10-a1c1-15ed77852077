import 'package:flutter/material.dart';
import '../../constants/colors.dart';
import '../../models/patient.dart';
import '../../services/patient_service.dart';
import '../../widgets/custom_text_field.dart';

class PatientFormScreen extends StatefulWidget {
  final Function()? onPatientAdded;
  final Patient? patient; // Patient à modifier (null si création)

  const PatientFormScreen({
    Key? key, 
    this.onPatientAdded,
    this.patient,
  }) : super(key: key);

  @override
  State<PatientFormScreen> createState() => _PatientFormScreenState();
}

class _PatientFormScreenState extends State<PatientFormScreen> {
  final _formKey = GlobalKey<FormState>();
  int _currentStep = 0;
  bool _isLoading = false;

  // Contrôleurs pour tous les champs
  final _lastnameController = TextEditingController();
  final _firstnameController = TextEditingController();
  final _sexeController = TextEditingController();
  final _birthDateController = TextEditingController();
  final _birthPlaceController = TextEditingController();
  final _telController = TextEditingController();
  final _addressController = TextEditingController();
  final _husbandNameController = TextEditingController();
  final _husbandTelController = TextEditingController();
  final _assuranceController = TextEditingController();
  final _occupationController = TextEditingController();
  final _studyLevelController = TextEditingController();
  final _languageController = TextEditingController();
  // Contrôleurs pour les informations de grossesse
  final _lastMenstrualPeriodController = TextEditingController();
  final _gestationalAgeController = TextEditingController();
  final _firstCpnDateController = TextEditingController();
  final _pregnancyTermController = TextEditingController();
  final _expectedDeliveryDateController = TextEditingController();
  final _nextAppointmentDateController = TextEditingController();
  final _pregnancyEndDateController = TextEditingController();
  bool _smsConsent = false;

  Color get _stepBackgroundColor => AppColors.primaryBlue.withOpacity(0.07);

  @override
  void initState() {
    super.initState();
    if (widget.patient != null) {
      // Pré-remplir les champs si on modifie un patient existant
      _lastnameController.text = widget.patient!.lastname;
      _firstnameController.text = widget.patient!.firstname;
      _sexeController.text = widget.patient!.sexe;
      _birthDateController.text = widget.patient!.birthDate;
      _birthPlaceController.text = widget.patient!.birthPlace;
      _telController.text = widget.patient!.tel;
      _addressController.text = widget.patient!.address;
      _husbandNameController.text = widget.patient!.husbandName;
      _husbandTelController.text = widget.patient!.husbandTel;
      _assuranceController.text = widget.patient!.assurance;
      _occupationController.text = widget.patient!.occupation;
      _studyLevelController.text = widget.patient!.studyLevel;
      _languageController.text = widget.patient!.language;
      _firstCpnDateController.text = widget.patient!.firstCpnDate;
      _pregnancyTermController.text = widget.patient!.pregnancyTerm.toString();
      _expectedDeliveryDateController.text = widget.patient!.expectedDeliveryDate;
      _nextAppointmentDateController.text = widget.patient!.nextAppointmentDate;
      _pregnancyEndDateController.text = widget.patient!.pregnancyEndDate;
      _smsConsent = widget.patient!.smsConsent == 1;
    }
  }

  @override
  void dispose() {
    // Libérer les contrôleurs
    _lastnameController.dispose();
    _firstnameController.dispose();
    _sexeController.dispose();
    _birthDateController.dispose();
    _birthPlaceController.dispose();
    _telController.dispose();
    _addressController.dispose();
    _husbandNameController.dispose();
    _husbandTelController.dispose();
    _assuranceController.dispose();
    _occupationController.dispose();
    _studyLevelController.dispose();
    _languageController.dispose();
    _lastMenstrualPeriodController.dispose();
    _gestationalAgeController.dispose();
    _firstCpnDateController.dispose();
    _pregnancyTermController.dispose();
    _expectedDeliveryDateController.dispose();
    _nextAppointmentDateController.dispose();
    _pregnancyEndDateController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_formKey.currentState!.validate()) {
      setState(() {
        _currentStep++;
      });
    }
  }

  void _previousStep() {
    setState(() {
      _currentStep--;
    });
  }

  Future<void> _savePatient() async {
    setState(() => _isLoading = true);
    try {
      final now = DateTime.now().toIso8601String();
      final patient = Patient(
        id: widget.patient?.id, // Garder l'ID si modification
        serverId: widget.patient?.serverId, // Garder le serverId si modification
        firstname: _firstnameController.text,
        lastname: _lastnameController.text,
        sexe: _sexeController.text,
        birthDate: _birthDateController.text,
        birthPlace: _birthPlaceController.text,
        tel: _telController.text,
        address: _addressController.text,
        husbandName: _husbandNameController.text,
        husbandTel: _husbandTelController.text,
        assurance: _assuranceController.text,
        occupation: _occupationController.text,
        studyLevel: _studyLevelController.text,
        language: _languageController.text,
        firstCpnDate: _firstCpnDateController.text,
        pregnancyTerm: int.tryParse(_pregnancyTermController.text) ?? 0,
        expectedDeliveryDate: _expectedDeliveryDateController.text,
        nextAppointmentDate: _nextAppointmentDateController.text,
        pregnancyEndDate: _pregnancyEndDateController.text,
        smsConsent: _smsConsent ? 1 : 0,
        agentId: widget.patient?.agentId ?? 1, // Garder l'agent_id ou utiliser la valeur par défaut
        agentServerId: widget.patient?.agentServerId ?? 1,
        createdAt: widget.patient?.createdAt ?? now, // Garder la date de création originale
        updatedAt: now,
        sync_status: 0, // Marquer comme non synchronisé après modification
      );

      if (widget.patient != null) {
        // Mise à jour
        await PatientService().updatePatient(patient);
      } else {
        // Création
        await PatientService().addPatient(patient);
      }

      if (widget.onPatientAdded != null) widget.onPatientAdded!();
      Navigator.pop(context, true);
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Erreur lors de l\'enregistrement : $e'), backgroundColor: Colors.red),
      );
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        title: Text(
          widget.patient != null ? 'Modifier le patient' : 'Ajouter un patient',
          style: TextStyle(fontWeight: FontWeight.bold, color: Colors.white)
        ),
        centerTitle: true,
        backgroundColor: AppColors.primaryBlue,
        elevation: 1,
        iconTheme: const IconThemeData(color: Colors.white),
      ),
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(16.0),
          child: Form(
            key: _formKey,
            child: Theme(
              data: Theme.of(context).copyWith(
                colorScheme: Theme.of(context).colorScheme.copyWith(
                  primary: AppColors.primaryBlue,
                  secondary: AppColors.primaryBlue,
                ),
              ),
              child: Column(
                children: [
                  // Indicateur de progression
                  Container(
                    padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 20),
                    child: Row(
                      children: List.generate(4, (index) {
                        return Expanded(
                          child: Container(
                            margin: EdgeInsets.symmetric(horizontal: 4),
                            child: Column(
                              children: [
                                Container(
                                  height: 4,
                                  decoration: BoxDecoration(
                                    color: index <= _currentStep
                                        ? AppColors.primaryBlue
                                        : Colors.grey.shade300,
                                    borderRadius: BorderRadius.circular(2),
                                  ),
                                ),
                                SizedBox(height: 8),
                                Text(
                                  'Étape ${index + 1}',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: index <= _currentStep
                                        ? AppColors.primaryBlue
                                        : Colors.grey.shade600,
                                    fontWeight: index == _currentStep
                                        ? FontWeight.bold
                                        : FontWeight.normal,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        );
                      }),
                    ),
                  ),

                  // Contenu de l'étape actuelle
                  Expanded(
                    child: SingleChildScrollView(
                      padding: const EdgeInsets.all(16),
                      child: _buildCurrentStepContent(),
                    ),
                  ),

                  // Boutons de navigation
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: Colors.white,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.grey.withOpacity(0.2),
                          spreadRadius: 1,
                          blurRadius: 5,
                          offset: Offset(0, -2),
                        ),
                      ],
                    ),
                    child: Row(
                      children: [
                        // Bouton Précédent
                        if (_currentStep > 0)
                          Expanded(
                            child: OutlinedButton(
                              onPressed: _previousStep,
                              style: OutlinedButton.styleFrom(
                                side: BorderSide(color: AppColors.primaryBlue),
                                padding: EdgeInsets.symmetric(vertical: 16),
                                shape: RoundedRectangleBorder(
                                  borderRadius: BorderRadius.circular(8),
                                ),
                              ),
                              child: Text(
                                "Précédent",
                                style: TextStyle(
                                  color: AppColors.primaryBlue,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ),
                          ),

                        if (_currentStep > 0) SizedBox(width: 16),

                        // Bouton Suivant/Enregistrer
                        Expanded(
                          child: ElevatedButton(
                            onPressed: _isLoading ? null : (_currentStep < 3 ? _nextStep : _savePatient),
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppColors.primaryBlue,
                              padding: EdgeInsets.symmetric(vertical: 16),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(8),
                              ),
                            ),
                            child: _isLoading
                                ? SizedBox(
                                    width: 20,
                                    height: 20,
                                    child: CircularProgressIndicator(
                                      color: Colors.white,
                                      strokeWidth: 2,
                                    ),
                                  )
                                : Text(
                                    _currentStep < 3 ? "Suivant" : "Enregistrer",
                                    style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600,
                                      fontSize: 16,
                                    ),
                                  ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
            ),
          ),
        ),
      ),
    )
  }

  Widget _buildCurrentStepContent() {
    switch (_currentStep) {
      case 0:
        return _buildPersonalInfoStep();
      case 1:
        return _buildPregnancyInfoStep();
      case 2:
        return _buildFamilyInsuranceStep();
      case 3:
        return _buildSummaryStep();
      default:
        return Container();
    }
  }

  Widget _buildPersonalInfoStep() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppColors.primaryBlue.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.person,
                  color: AppColors.primaryBlue,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Informations personnelles',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  Text(
                    'Renseignez les informations de base du patient',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 24),
          _buildTextField(_lastnameController, "Nom", "Entrez le nom", true),
          _buildTextField(_firstnameController, "Prénom", "Entrez le prénom", true),
          _buildTextField(_sexeController, "Sexe", "M/F", true),
          _buildTextField(_birthDateController, "Date de naissance", "YYYY-MM-DD", true, isDate: true),
          _buildTextField(_birthPlaceController, "Lieu de naissance", "Entrez le lieu", false),
          _buildTextField(_addressController, "Adresse", "Entrez l'adresse", false),
          _buildTextField(_telController, "Téléphone", "Entrez le téléphone", false, keyboardType: TextInputType.phone),
          _buildTextField(_languageController, "Langue", "Entrez la langue", false),
          _buildTextField(_studyLevelController, "Niveau d'étude", "Entrez le niveau", false),
          _buildTextField(_occupationController, "Profession", "Entrez la profession", false),
        ],
      ),
    );
  }

  Widget _buildPregnancyInfoStep() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.pink.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.pregnant_woman,
                  color: Colors.pink,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Informations grossesse',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  Text(
                    'Informations liées à la grossesse (optionnel)',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 24),
          _buildTextField(_lastMenstrualPeriodController, "Dernières règles", "YYYY-MM-DD", false, isDate: true),
          _buildTextField(_gestationalAgeController, "Âge gestationnel", "En semaines", false, keyboardType: TextInputType.number),
          _buildTextField(_expectedDeliveryDateController, "Date prévue d'accouchement", "YYYY-MM-DD", false, isDate: true),
        ],
      ),
    );
  }

  Widget _buildFamilyInsuranceStep() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.green.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.family_restroom,
                  color: Colors.green,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Famille & Assurance',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  Text(
                    'Informations familiales et d\'assurance',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 24),
          _buildTextField(_husbandNameController, "Nom du mari", "Entrez le nom du mari", false),
          _buildTextField(_husbandTelController, "Téléphone du mari", "Entrez le téléphone", false, keyboardType: TextInputType.phone),
          _buildTextField(_assuranceController, "Assurance", "Entrez l'assurance", false),
        ],
      ),
    );
  }

  Widget _buildSummaryStep() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.grey.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.grey.shade200),
      ),
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.summarize,
                  color: Colors.orange,
                  size: 24,
                ),
              ),
              SizedBox(width: 12),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Résumé & Consentement',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.bold,
                      color: Colors.grey.shade800,
                    ),
                  ),
                  Text(
                    'Vérifiez les informations et donnez votre consentement',
                    style: TextStyle(
                      fontSize: 14,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ],
          ),
          SizedBox(height: 24),
          _buildSummary(),
          SizedBox(height: 20),
          Container(
            padding: EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.blue.shade50,
              borderRadius: BorderRadius.circular(8),
              border: Border.all(color: Colors.blue.shade200),
            ),
            child: Row(
              children: [
                Checkbox(
                  value: _smsConsent,
                  onChanged: (value) => setState(() => _smsConsent = value ?? false),
                  activeColor: AppColors.primaryBlue,
                ),
                Expanded(
                  child: Text(
                    "J'accepte de recevoir des SMS de rappel pour les rendez-vous et le suivi médical",
                    style: TextStyle(
                      color: Colors.blue.shade800,
                      fontSize: 14,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTextField(TextEditingController controller, String label, String hint, bool required, {TextInputType keyboardType = TextInputType.text, bool isDate = false}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16.0),
      child: isDate
          ? GestureDetector(
              onTap: () async {
                DateTime? picked = await showDatePicker(
                  context: context,
                  initialDate: DateTime.now(),
                  firstDate: DateTime(1900),
                  lastDate: DateTime(2100),
                  builder: (context, child) {
                    return Theme(
                      data: Theme.of(context).copyWith(
                        colorScheme: ColorScheme.light(
                          primary: AppColors.primaryBlue,
                          onPrimary: Colors.white,
                          onSurface: Colors.black,
                        ),
                        dialogBackgroundColor: Colors.white,
                      ),
                      child: child!,
                    );
                  },
                );
                if (picked != null) {
                  controller.text = picked.toIso8601String().substring(0, 10);
                }
              },
              child: AbsorbPointer(
                child: CustomTextField(
                  label: label,
                  controller: controller,
                  hintText: hint,
                  keyboardType: keyboardType,
                  validator: required
                      ? (value) => value == null || value.isEmpty ? 'Champ obligatoire' : null
                      : null,
                  suffixIcon: Icon(Icons.calendar_today, color: AppColors.primaryBlue),
                ),
              ),
            )
          : CustomTextField(
              label: label,
              controller: controller,
              hintText: hint,
              keyboardType: keyboardType,
              validator: required
                  ? (value) => value == null || value.isEmpty ? 'Champ obligatoire' : null
                  : null,
            ),
    );
  }

  Widget _buildSummary() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Informations personnelles
        _buildSummarySection(
          "Informations personnelles",
          Icons.person,
          Colors.blue,
          [
            if (_lastnameController.text.isNotEmpty) "Nom : ${_lastnameController.text}",
            if (_firstnameController.text.isNotEmpty) "Prénom : ${_firstnameController.text}",
            if (_sexeController.text.isNotEmpty) "Sexe : ${_sexeController.text}",
            if (_birthDateController.text.isNotEmpty) "Date de naissance : ${_birthDateController.text}",
            if (_birthPlaceController.text.isNotEmpty) "Lieu de naissance : ${_birthPlaceController.text}",
            if (_addressController.text.isNotEmpty) "Adresse : ${_addressController.text}",
            if (_telController.text.isNotEmpty) "Téléphone : ${_telController.text}",
            if (_languageController.text.isNotEmpty) "Langue : ${_languageController.text}",
            if (_studyLevelController.text.isNotEmpty) "Niveau d'étude : ${_studyLevelController.text}",
            if (_occupationController.text.isNotEmpty) "Profession : ${_occupationController.text}",
          ],
        ),

        SizedBox(height: 16),

        // Informations grossesse
        _buildSummarySection(
          "Informations grossesse",
          Icons.pregnant_woman,
          Colors.pink,
          [
            if (_lastMenstrualPeriodController.text.isNotEmpty) "Dernières règles : ${_lastMenstrualPeriodController.text}",
            if (_gestationalAgeController.text.isNotEmpty) "Âge gestationnel : ${_gestationalAgeController.text} semaines",
            if (_expectedDeliveryDateController.text.isNotEmpty) "Date prévue d'accouchement : ${_expectedDeliveryDateController.text}",
          ],
        ),

        SizedBox(height: 16),

        // Famille & Assurance
        _buildSummarySection(
          "Famille & Assurance",
          Icons.family_restroom,
          Colors.green,
          [
            if (_husbandNameController.text.isNotEmpty) "Nom du mari : ${_husbandNameController.text}",
            if (_husbandTelController.text.isNotEmpty) "Téléphone du mari : ${_husbandTelController.text}",
            if (_assuranceController.text.isNotEmpty) "Assurance : ${_assuranceController.text}",
          ],
        ),
      ],
    );
  }

  Widget _buildSummarySection(String title, IconData icon, Color color, List<String> items) {
    if (items.isEmpty) return SizedBox.shrink();

    return Container(
      padding: EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              SizedBox(width: 8),
              Text(
                title,
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: Colors.grey.shade800,
                ),
              ),
            ],
          ),
          SizedBox(height: 12),
          ...items.map((item) => Padding(
            padding: EdgeInsets.only(bottom: 4),
            child: Text(
              item,
              style: TextStyle(
                fontSize: 14,
                color: Colors.grey.shade700,
              ),
            ),
          )).toList(),
        ],
      ),
    );
  }
}
