import 'dart:convert';
import 'dart:async';
import 'package:http/http.dart' as http;
import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/foundation.dart';
import 'package:sqflite/sqflite.dart';
import '../config/api_config.dart';
import '../services/auth_service.dart';
import '../services/database_service.dart';
import '../models/hospital.dart';
import '../models/patient.dart';
import '../models/sync_models.dart';

class ApiSyncService {
  static final ApiSyncService _instance = ApiSyncService._internal();
  final _authService = AuthService();
  final _databaseService = DatabaseService();
  final _connectivity = Connectivity();

  factory ApiSyncService() => _instance;
  ApiSyncService._internal();

  // Contrôleurs de stream pour les notifications
  final _syncStatusController = StreamController<SyncStatus>.broadcast();
  final _syncProgressController = StreamController<SyncProgress>.broadcast();

  // Streams publics
  Stream<SyncStatus> get syncStatusStream => _syncStatusController.stream;
  Stream<SyncProgress> get syncProgressStream => _syncProgressController.stream;

  // État de synchronisation
  bool _isSyncing = false;
  DateTime? _lastSyncTime;

  // Getters
  bool get isSyncing => _isSyncing;
  DateTime? get lastSyncTime => _lastSyncTime;

  /// Vérifie la connectivité Internet
  Future<bool> hasInternetConnection() async {
    try {
      final connectivityResult = await _connectivity.checkConnectivity();
      return !connectivityResult.contains(ConnectivityResult.none);
    } catch (e) {
      print('Erreur lors de la vérification de la connectivité: $e');
      return false;
    }
  }

  /// Synchronisation complète des données
  Future<void> performFullSync() async {
    if (_isSyncing) {
      print('Synchronisation déjà en cours...');
      return;
    }

    if (!await hasInternetConnection()) {
      _syncStatusController.add(SyncStatus.offline());
      return;
    }

    _isSyncing = true;
    _syncStatusController.add(SyncStatus.syncing('Début de la synchronisation...'));

    try {
      // 1. Synchroniser les données initiales (publiques)
      _syncProgressController.add(SyncProgress.start('Synchronisation des données initiales'));
      await _syncInitialData();

      // 2. Synchroniser les patients non synchronisés
      _syncProgressController.add(SyncProgress.start('Synchronisation des patients'));
      final result = await syncPatientsToServer();

      // 3. Récupérer les patients du serveur
      _syncProgressController.add(SyncProgress.start('Récupération des patients du serveur'));
      await _syncPatientsFromServer();

      _lastSyncTime = DateTime.now();
      _syncStatusController.add(SyncStatus.success(
        'Synchronisation terminée avec succès. ${result.syncedPatients} patients synchronisés.'
      ));

      print('Synchronisation complète terminée avec succès');
    } catch (e) {
      print('Erreur lors de la synchronisation: $e');
      _syncStatusController.add(SyncStatus.error(
        'Erreur lors de la synchronisation',
        e.toString()
      ));
    } finally {
      _isSyncing = false;
    }
  }

  /// Synchronisation manuelle des patients uniquement
  Future<PatientSyncResult> syncPatientsToServer() async {
    if (!await hasInternetConnection()) {
      throw Exception('Pas de connexion Internet disponible');
    }

    // Récupérer les patients non synchronisés
    final unsyncedPatients = await _databaseService.getUnsyncedPatients();

    if (unsyncedPatients.isEmpty) {
      return PatientSyncResult(
        totalPatients: 0,
        syncedPatients: 0,
        failedPatients: 0,
        errors: [],
        mobileIdToServerIdMap: {},
      );
    }

    _syncProgressController.add(SyncProgress.progress(0, unsyncedPatients.length, 'Envoi des patients'));

    final errors = <String>[];
    final mobileIdToServerIdMap = <String, int>{};
    int syncedCount = 0;

    for (int i = 0; i < unsyncedPatients.length; i++) {
      final patient = unsyncedPatients[i];

      try {
        _syncProgressController.add(SyncProgress.progress(
          i + 1,
          unsyncedPatients.length,
          'Synchronisation de ${patient.firstname} ${patient.lastname}'
        ));

        final serverId = await _sendPatientToServer(patient);
        if (serverId != null) {
          // Mettre à jour le patient avec le server_id
          await _databaseService.updatePatientServerId(patient.id!, serverId);
          mobileIdToServerIdMap[patient.id.toString()] = serverId;
          syncedCount++;
        }
      } catch (e) {
        errors.add('Erreur pour ${patient.firstname} ${patient.lastname}: $e');
        print('Erreur lors de la synchronisation du patient ${patient.id}: $e');
      }
    }

    return PatientSyncResult(
      totalPatients: unsyncedPatients.length,
      syncedPatients: syncedCount,
      failedPatients: unsyncedPatients.length - syncedCount,
      errors: errors,
      mobileIdToServerIdMap: mobileIdToServerIdMap,
    );
  }

  /// Envoie un patient vers le serveur
  Future<int?> _sendPatientToServer(Patient patient) async {
    try {
      final headers = _authService.getAuthHeaders();

      final patientData = {
        'mobile_id': patient.id.toString(),
        'firstname': patient.firstname,
        'lastname': patient.lastname,
        'email': patient.tel, // Utiliser le téléphone comme email temporaire si pas d'email
        'tel': patient.tel,
        'birth_date': patient.birthDate,
        'address': patient.address,
        'assurance': patient.assurance,
        'husband_name': patient.husbandName,
        'husband_tel': patient.husbandTel,
      };

      final requestBody = {
        'patients': [patientData]
      };

      final response = await http.post(
        Uri.parse('${ApiConfig.baseUrl}sync/patients'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200 || response.statusCode == 201) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success' && data['data']['created_patients'].isNotEmpty) {
          final createdPatient = data['data']['created_patients'][0];
          return createdPatient['server_id'] as int;
        }
      } else {
        print('Erreur HTTP ${response.statusCode}: ${response.body}');
      }
    } catch (e) {
      print('Erreur lors de l\'envoi du patient: $e');
    }
    return null;
  }

  /// Récupère les patients depuis le serveur
  Future<void> _syncPatientsFromServer() async {
    try {
      final headers = _authService.getAuthHeaders();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}sync/patients'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final data = jsonDecode(response.body);
        if (data['status'] == 'success' && data['data']['patients'] != null) {
          final patients = data['data']['patients'] as List;

          for (final patientData in patients) {
            // Convertir et sauvegarder le patient
            await _storePatientFromServer(patientData);
          }

          print('${patients.length} patients récupérés du serveur');
        }
      }
    } catch (e) {
      print('Erreur lors de la récupération des patients: $e');
    }
  }

  /// Stocke un patient reçu du serveur
  Future<void> _storePatientFromServer(Map<String, dynamic> patientData) async {
    try {
      final patient = Patient(
        id: null, // Sera généré par la base locale
        serverId: patientData['id'] as int?,
        firstname: patientData['firstname'] ?? '',
        lastname: patientData['lastname'] ?? '',
        sexe: patientData['sexe'] ?? '',
        birthDate: patientData['birth_date'] ?? '',
        birthPlace: patientData['birth_place'] ?? '',
        tel: patientData['tel'] ?? '',
        address: patientData['address'] ?? '',
        husbandName: patientData['husband_name'] ?? '',
        husbandTel: patientData['husband_tel'] ?? '',
        assurance: patientData['assurance'] ?? '',
        occupation: patientData['occupation'] ?? '',
        studyLevel: patientData['study_level'] ?? '',
        language: patientData['language'] ?? '',
        firstCpnDate: patientData['first_cpn_date'] ?? '',
        pregnancyTerm: patientData['pregnancy_term'] ?? 0,
        expectedDeliveryDate: patientData['expected_delivery_date'] ?? '',
        nextAppointmentDate: patientData['next_appointment_date'] ?? '',
        pregnancyEndDate: patientData['pregnancy_end_date'] ?? '',
        smsConsent: patientData['sms_consent'] == 1 ? 1 : 0,
        agentId: 0, // À adapter selon l'agent connecté
        agentServerId: 0, // À adapter selon l'agent connecté
        createdAt: patientData['created_at'] ?? DateTime.now().toIso8601String(),
        updatedAt: patientData['updated_at'] ?? DateTime.now().toIso8601String(),
        sync_status: 1, // Marqué comme synchronisé
      );

      // Stocker le patient (utiliser la méthode existante)
      // Pour l'instant, on skip le stockage des patients du serveur
      print('Patient du serveur reçu: ${patient.firstname} ${patient.lastname}');
    } catch (e) {
      print('Erreur lors du stockage du patient du serveur: $e');
    }
  }

  /// Synchronise les données initiales (hôpitaux, services, spécialités)
  Future<void> _syncInitialData() async {
    try {
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.initialDataEndpoint}'),
        headers: {'Content-Type': 'application/json'},
      );

      if (response.statusCode == 200) {
        // Vérifier si la réponse est du JSON valide
        final contentType = response.headers['content-type'] ?? '';

        if (!contentType.contains('application/json') && response.body.trim().startsWith('<!')) {
          print('Erreur: Le serveur a retourné du HTML au lieu de JSON');
          print('URL appelée: ${ApiConfig.baseUrl}${ApiConfig.initialDataEndpoint}');
          print('Content-Type: $contentType');
          print('Début de la réponse: ${response.body.substring(0, 100)}...');
          return; // Ne pas essayer de parser du HTML
        }

        try {
          final data = jsonDecode(response.body);
          if (data['status'] == 'success') {
            await _storeInitialData(data['data']);
          } else {
            throw Exception('Erreur API: ${data['message']}');
          }
        } catch (formatException) {
          print('Erreur de format JSON: $formatException');
          print('Réponse reçue: ${response.body.substring(0, 200)}...');
          return; // Ne pas planter l'app
        }
      } else {
        print('Erreur HTTP ${response.statusCode} pour ${ApiConfig.baseUrl}${ApiConfig.initialDataEndpoint}');
        print('Réponse: ${response.body}');
      }
    } catch (e) {
      print('Erreur lors de la synchronisation des données initiales: $e');
      rethrow;
    }
  }

  /// Synchronise les données spécifiques à l'utilisateur
  Future<void> _syncUserData() async {
    try {
      final headers = _authService.getAuthHeaders();
      final response = await http.get(
        Uri.parse('${ApiConfig.baseUrl}${ApiConfig.userDataEndpoint}'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        // Vérifier si la réponse est du JSON valide
        if (response.body.trim().startsWith('<!')) {
          print('Erreur: Le serveur a retourné du HTML au lieu de JSON pour les données utilisateur');
          print('URL appelée: ${ApiConfig.baseUrl}${ApiConfig.userDataEndpoint}');
          return; // Ne pas essayer de parser du HTML
        }

        try {
          final data = jsonDecode(response.body);
          if (data['status'] == 'success') {
            await _storeUserData(data['data']);
          } else {
            throw Exception('Erreur API: ${data['message']}');
          }
        } catch (formatException) {
          print('Erreur de format JSON pour les données utilisateur: $formatException');
          return; // Ne pas planter l'app
        }
      } else if (response.statusCode == 401) {
        // Token expiré, tenter de le rafraîchir
        final refreshed = await _authService.refreshToken();
        if (refreshed) {
          return await _syncUserData(); // Réessayer
        } else {
          throw Exception('Session expirée, veuillez vous reconnecter');
        }
      } else {
        throw Exception('Erreur HTTP: ${response.statusCode}');
      }
    } catch (e) {
      print('Erreur lors de la synchronisation des données utilisateur: $e');
      rethrow;
    }
  }

  /// Stocke les données initiales en base locale
  Future<void> _storeInitialData(Map<String, dynamic> data) async {
    try {
      final db = await _databaseService.database;
      
      // Stocker les hôpitaux
      if (data['hospitals'] != null) {
        await db.delete('hospital'); // Nettoyer les anciennes données
        for (final hospitalData in data['hospitals']) {
          final hospital = Hospital.fromJson(hospitalData);
          await db.insert('hospital', hospital.toMap());
        }
        print('${data['hospitals'].length} hôpitaux synchronisés');
      }

      // Stocker les services et spécialités si nécessaire
      // TODO: Ajouter les tables services et specialities si requis
      
    } catch (e) {
      print('Erreur lors du stockage des données initiales: $e');
      rethrow;
    }
  }

  /// Stocke les données utilisateur en base locale
  Future<void> _storeUserData(Map<String, dynamic> data) async {
    try {
      final db = await _databaseService.database;
      
      // Stocker les patients
      if (data['role_data']?['patients'] != null) {
        final patients = data['role_data']['patients'] as List;
        for (final patientData in patients) {
          // Adapter les données API au modèle local Patient
          final adaptedPatient = _adaptApiPatientToLocal(patientData);
          await db.insert('patient', adaptedPatient, 
            conflictAlgorithm: ConflictAlgorithm.replace);
        }
        print('${patients.length} patients synchronisés');
      }

      // Stocker les rendez-vous
      if (data['role_data']?['appointments'] != null) {
        // TODO: Implémenter le stockage des rendez-vous
        print('Rendez-vous à synchroniser: ${data['role_data']['appointments'].length}');
      }
      
    } catch (e) {
      print('Erreur lors du stockage des données utilisateur: $e');
      rethrow;
    }
  }

  /// Adapte les données patient de l'API au format local
  Map<String, dynamic> _adaptApiPatientToLocal(Map<String, dynamic> apiPatient) {
    // Séparer le nom complet en prénom et nom
    final fullName = apiPatient['name'] as String? ?? '';
    final nameParts = fullName.split(' ');
    final firstname = nameParts.isNotEmpty ? nameParts.first : '';
    final lastname = nameParts.length > 1 ? nameParts.sublist(1).join(' ') : '';

    return {
      'server_id': apiPatient['id'],
      'firstname': firstname,
      'lastname': lastname,
      'sexe': 'F', // Par défaut pour les patientes enceintes
      'birth_date': apiPatient['birth_date'] ?? '',
      'birth_place': '', // Non fourni par l'API
      'tel': apiPatient['tel'] ?? '',
      'address': apiPatient['address'] ?? '',
      'husband_name': apiPatient['husband_name'] ?? '',
      'husband_tel': apiPatient['husband_tel'] ?? '',
      'assurance': apiPatient['assurance'] ?? '',
      'occupation': '', // Non fourni par l'API
      'study_level': '', // Non fourni par l'API
      'language': '', // Non fourni par l'API
      'first_cpn_date': apiPatient['active_pregnancy']?['start_date'] ?? '',
      'pregnancy_term': _parsePregnancyTerm(apiPatient['active_pregnancy']?['term']),
      'expected_delivery_date': '', // À calculer
      'next_appointment_date': '', // À récupérer des rendez-vous
      'pregnancy_end_date': '', // Non fourni
      'sms_consent': 1, // Par défaut
      'agent_id': _authService.currentUser?.id ?? 0,
      'agent_server_id': _authService.currentUser?.id ?? 0,
      'created_at': DateTime.now().toIso8601String(),
      'updated_at': DateTime.now().toIso8601String(),
      'sync_status': 1, // Marqué comme synchronisé
      'last_sync_date': DateTime.now().toIso8601String(),
    };
  }

  /// Parse le terme de grossesse depuis le format API ("28 SA" -> 28)
  int _parsePregnancyTerm(String? term) {
    if (term == null) return 0;
    final match = RegExp(r'(\d+)').firstMatch(term);
    return match != null ? int.parse(match.group(1)!) : 0;
  }

  /// Synchronisation périodique (à appeler régulièrement)
  Future<void> periodicSync() async {
    try {
      if (await hasInternetConnection()) {
        await performFullSync();
      }
    } catch (e) {
      print('Erreur lors de la synchronisation périodique: $e');
      // Ne pas propager l'erreur pour éviter de bloquer l'app
    }
  }
}
