/// Modèles pour la synchronisation
enum SyncStatusType {
  idle,
  syncing,
  success,
  error,
  offline
}

class SyncStatus {
  final SyncStatusType type;
  final String message;
  final DateTime timestamp;
  final String? errorDetails;

  SyncStatus({
    required this.type,
    required this.message,
    DateTime? timestamp,
    this.errorDetails,
  }) : timestamp = timestamp ?? DateTime.now();

  factory SyncStatus.idle() => SyncStatus(
    type: SyncStatusType.idle,
    message: 'En attente de synchronisation',
  );

  factory SyncStatus.syncing(String message) => SyncStatus(
    type: SyncStatusType.syncing,
    message: message,
  );

  factory SyncStatus.success(String message) => SyncStatus(
    type: SyncStatusType.success,
    message: message,
  );

  factory SyncStatus.error(String message, [String? errorDetails]) => SyncStatus(
    type: SyncStatusType.error,
    message: message,
    errorDetails: errorDetails,
  );

  factory SyncStatus.offline() => SyncStatus(
    type: SyncStatusType.offline,
    message: 'Hors ligne - synchronisation en attente',
  );
}

class SyncProgress {
  final int current;
  final int total;
  final String operation;
  final double percentage;

  SyncProgress({
    required this.current,
    required this.total,
    required this.operation,
  }) : percentage = total > 0 ? (current / total) * 100 : 0;

  factory SyncProgress.start(String operation) => SyncProgress(
    current: 0,
    total: 1,
    operation: operation,
  );

  factory SyncProgress.progress(int current, int total, String operation) => SyncProgress(
    current: current,
    total: total,
    operation: operation,
  );

  factory SyncProgress.complete(String operation) => SyncProgress(
    current: 1,
    total: 1,
    operation: operation,
  );
}

/// Résultat de synchronisation des patients
class PatientSyncResult {
  final int totalPatients;
  final int syncedPatients;
  final int failedPatients;
  final List<String> errors;
  final Map<String, int> mobileIdToServerIdMap;

  PatientSyncResult({
    required this.totalPatients,
    required this.syncedPatients,
    required this.failedPatients,
    required this.errors,
    required this.mobileIdToServerIdMap,
  });

  bool get hasErrors => errors.isNotEmpty;
  bool get isComplete => syncedPatients + failedPatients == totalPatients;
  double get successRate => totalPatients > 0 ? (syncedPatients / totalPatients) * 100 : 0;
}
